/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    min-height: 100vh;
    overflow: hidden;
}

#app {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Nickname Entry Page */
.nickname-container {
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.nickname-container h1 {
    font-size: 3rem;
    margin-bottom: 30px;
    color: #ff6b35;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.nickname-input {
    padding: 15px 20px;
    font-size: 1.2rem;
    border: none;
    border-radius: 8px;
    margin: 10px;
    width: 300px;
    text-align: center;
}

.btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    border: none;
    border-radius: 8px;
    background: #ff6b35;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px;
}

.btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

/* Waiting Room */
.waiting-container {
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    min-width: 600px;
}

.player-counter {
    font-size: 2rem;
    margin: 20px 0;
    color: #4CAF50;
}

.timer {
    font-size: 1.5rem;
    margin: 20px 0;
    color: #ff6b35;
}

.players-list {
    margin: 20px 0;
    text-align: left;
}

.player-item {
    padding: 10px;
    margin: 5px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* Chat System */
.chat-container {
    margin-top: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
}

.chat-messages {
    height: 200px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    text-align: left;
}

.chat-message {
    margin: 5px 0;
    padding: 5px;
}

.chat-message .username {
    font-weight: bold;
    color: #4CAF50;
}

.chat-input-container {
    display: flex;
    gap: 10px;
}

.chat-input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

/* Game Container */
.game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    background: #2d5016;
    overflow: hidden;
}

.game-board {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(15, 40px);
    grid-template-rows: repeat(13, 40px);
    gap: 0;
    border: 3px solid #8B4513;
    background: #228B22;
}

.cell {
    width: 40px;
    height: 40px;
    position: relative;
    background: #32CD32;
}

.wall {
    background: #8B4513;
    border: 1px solid #654321;
}

.block {
    background: #D2691E;
    border: 1px solid #A0522D;
    position: relative;
}

.block::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: #CD853F;
    border-radius: 2px;
}

/* Players */
.player {
    position: absolute;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 2px solid #000;
    top: 2px;
    left: 2px;
    z-index: 10;
    transition: all 0.1s ease;
}

.player-1 { background: #FF4444; }
.player-2 { background: #4444FF; }
.player-3 { background: #44FF44; }
.player-4 { background: #FFFF44; }

/* Bombs */
.bomb {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #333;
    border-radius: 50%;
    top: 5px;
    left: 5px;
    z-index: 5;
    animation: bomb-pulse 1s infinite;
}

@keyframes bomb-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.bomb::after {
    content: '';
    position: absolute;
    top: 5px;
    left: 5px;
    width: 20px;
    height: 20px;
    background: #666;
    border-radius: 50%;
}

/* Explosions */
.explosion {
    position: absolute;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, #FFD700 0%, #FF4500 50%, #FF0000 100%);
    z-index: 8;
    animation: explosion-anim 0.5s ease-out;
}

@keyframes explosion-anim {
    0% { transform: scale(0); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
    100% { transform: scale(1); opacity: 0; }
}

/* Power-ups */
.powerup {
    position: absolute;
    width: 30px;
    height: 30px;
    border-radius: 5px;
    top: 5px;
    left: 5px;
    z-index: 3;
    animation: powerup-glow 2s infinite;
}

@keyframes powerup-glow {
    0%, 100% { transform: scale(1); box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
    50% { transform: scale(1.1); box-shadow: 0 0 15px rgba(255, 255, 255, 0.8); }
}

.powerup-bombs { background: #FF6B35; }
.powerup-flames { background: #FF1744; }
.powerup-speed { background: #00E676; }

/* UI Elements */
.game-ui {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 20;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 10px;
}

.player-stats {
    margin: 5px 0;
    font-size: 1.1rem;
}

.lives {
    color: #FF4444;
}

.game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    z-index: 30;
}

.game-over h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #ff6b35;
}

/* Responsive design */
@media (max-width: 768px) {
    .game-board {
        grid-template-columns: repeat(15, 30px);
        grid-template-rows: repeat(13, 30px);
    }
    
    .cell {
        width: 30px;
        height: 30px;
    }
    
    .player {
        width: 26px;
        height: 26px;
    }
    
    .bomb {
        width: 22px;
        height: 22px;
        top: 4px;
        left: 4px;
    }
}
