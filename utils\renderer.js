import { createElement } from '../framework/dom.js';

export class GameRenderer {
    constructor() {
        this.gameBoard = null;
        this.cellElements = new Map();
        this.playerElements = new Map();
        this.bombElements = new Map();
        this.explosionElements = new Map();
        this.powerupElements = new Map();
        this.lastGameState = null;
        this.animationFrame = null;
    }

    initialize(gameBoard) {
        this.gameBoard = gameBoard;
        this.setupCells();
    }

    setupCells() {
        if (!this.gameBoard) return;

        // Clear existing cells
        this.gameBoard.innerHTML = '';
        this.cellElements.clear();

        // Create cell elements
        for (let y = 0; y < 13; y++) {
            for (let x = 0; x < 15; x++) {
                const cellId = `${x}-${y}`;
                const cellElement = document.createElement('div');
                cellElement.className = 'cell';
                cellElement.id = `cell-${cellId}`;
                cellElement.dataset.x = x;
                cellElement.dataset.y = y;
                
                this.gameBoard.appendChild(cellElement);
                this.cellElements.set(cellId, cellElement);
            }
        }
    }

    render(gameState) {
        if (!gameState || !this.gameBoard) return;

        // Cancel any pending animation frame
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        // Schedule rendering for next frame
        this.animationFrame = requestAnimationFrame(() => {
            this.performRender(gameState);
        });
    }

    performRender(gameState) {
        // Only update what has changed
        if (this.lastGameState) {
            this.updateChangedElements(gameState);
        } else {
            this.fullRender(gameState);
        }

        this.lastGameState = JSON.parse(JSON.stringify(gameState));
    }

    fullRender(gameState) {
        this.renderMap(gameState.map);
        this.renderPlayers(gameState.players);
        this.renderBombs(gameState.bombs);
        this.renderExplosions(gameState.explosions);
        this.renderPowerups(gameState.powerups);
    }

    updateChangedElements(gameState) {
        // Update map if changed
        if (this.hasMapChanged(gameState.map)) {
            this.renderMap(gameState.map);
        }

        // Update players
        this.updatePlayers(gameState.players);

        // Update bombs
        this.updateBombs(gameState.bombs);

        // Update explosions
        this.updateExplosions(gameState.explosions);

        // Update powerups
        this.updatePowerups(gameState.powerups);
    }

    hasMapChanged(map) {
        if (!this.lastGameState) return true;
        
        const lastMap = this.lastGameState.map;
        for (let y = 0; y < map.length; y++) {
            for (let x = 0; x < map[y].length; x++) {
                if (map[y][x] !== lastMap[y][x]) {
                    return true;
                }
            }
        }
        return false;
    }

    renderMap(map) {
        for (let y = 0; y < map.length; y++) {
            for (let x = 0; x < map[y].length; x++) {
                const cellId = `${x}-${y}`;
                const cellElement = this.cellElements.get(cellId);
                if (cellElement) {
                    const cellType = map[y][x];
                    cellElement.className = `cell ${cellType === 'wall' ? 'wall' : cellType === 'block' ? 'block' : ''}`;
                }
            }
        }
    }

    updatePlayers(players) {
        // Remove players that no longer exist
        for (const [playerId, element] of this.playerElements) {
            const player = players.find(p => p.id === playerId);
            if (!player || !player.isAlive) {
                element.remove();
                this.playerElements.delete(playerId);
            }
        }

        // Update or create player elements
        players.forEach((player, index) => {
            if (!player.isAlive) return;

            let playerElement = this.playerElements.get(player.id);
            
            if (!playerElement) {
                playerElement = document.createElement('div');
                playerElement.className = `player player-${index + 1}`;
                playerElement.id = `player-${player.id}`;
                this.playerElements.set(player.id, playerElement);
            }

            // Update position
            this.updatePlayerPosition(player, playerElement);
        });
    }

    updatePlayerPosition(player, element) {
        const cellId = `${Math.floor(player.x)}-${Math.floor(player.y)}`;
        const cellElement = this.cellElements.get(cellId);
        
        if (cellElement && !cellElement.contains(element)) {
            // Remove from old cell
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            // Add to new cell
            cellElement.appendChild(element);
        }

        // Update sub-pixel position
        const cellSize = 40;
        const offsetX = (player.x - Math.floor(player.x)) * cellSize;
        const offsetY = (player.y - Math.floor(player.y)) * cellSize;
        
        element.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
    }

    updateBombs(bombs) {
        // Remove bombs that no longer exist
        for (const [bombId, element] of this.bombElements) {
            const bomb = bombs.find(b => b.id === bombId);
            if (!bomb) {
                element.remove();
                this.bombElements.delete(bombId);
            }
        }

        // Update or create bomb elements
        bombs.forEach(bomb => {
            let bombElement = this.bombElements.get(bomb.id);
            
            if (!bombElement) {
                bombElement = document.createElement('div');
                bombElement.className = 'bomb';
                bombElement.id = `bomb-${bomb.id}`;
                
                const cellId = `${bomb.x}-${bomb.y}`;
                const cellElement = this.cellElements.get(cellId);
                if (cellElement) {
                    cellElement.appendChild(bombElement);
                }
                
                this.bombElements.set(bomb.id, bombElement);
            }
        });
    }

    updateExplosions(explosions) {
        // Remove explosions that no longer exist
        for (const [explosionId, element] of this.explosionElements) {
            const explosion = explosions.find(e => e.id === explosionId);
            if (!explosion) {
                element.remove();
                this.explosionElements.delete(explosionId);
            }
        }

        // Update or create explosion elements
        explosions.forEach(explosion => {
            let explosionElement = this.explosionElements.get(explosion.id);
            
            if (!explosionElement) {
                explosionElement = document.createElement('div');
                explosionElement.className = 'explosion';
                explosionElement.id = `explosion-${explosion.id}`;
                
                const cellId = `${explosion.x}-${explosion.y}`;
                const cellElement = this.cellElements.get(cellId);
                if (cellElement) {
                    cellElement.appendChild(explosionElement);
                }
                
                this.explosionElements.set(explosion.id, explosionElement);
            }
        });
    }

    updatePowerups(powerups) {
        // Remove powerups that no longer exist
        for (const [powerupId, element] of this.powerupElements) {
            const powerup = powerups.find(p => p.id === powerupId);
            if (!powerup) {
                element.remove();
                this.powerupElements.delete(powerupId);
            }
        }

        // Update or create powerup elements
        powerups.forEach(powerup => {
            let powerupElement = this.powerupElements.get(powerup.id);
            
            if (!powerupElement) {
                powerupElement = document.createElement('div');
                powerupElement.className = `powerup powerup-${powerup.type}`;
                powerupElement.id = `powerup-${powerup.id}`;
                
                const cellId = `${powerup.x}-${powerup.y}`;
                const cellElement = this.cellElements.get(cellId);
                if (cellElement) {
                    cellElement.appendChild(powerupElement);
                }
                
                this.powerupElements.set(powerup.id, powerupElement);
            }
        });
    }

    renderPlayers(players) {
        // Clear existing players
        this.playerElements.forEach(element => element.remove());
        this.playerElements.clear();

        this.updatePlayers(players);
    }

    renderBombs(bombs) {
        // Clear existing bombs
        this.bombElements.forEach(element => element.remove());
        this.bombElements.clear();

        this.updateBombs(bombs);
    }

    renderExplosions(explosions) {
        // Clear existing explosions
        this.explosionElements.forEach(element => element.remove());
        this.explosionElements.clear();

        this.updateExplosions(explosions);
    }

    renderPowerups(powerups) {
        // Clear existing powerups
        this.powerupElements.forEach(element => element.remove());
        this.powerupElements.clear();

        this.updatePowerups(powerups);
    }

    cleanup() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
        
        this.cellElements.clear();
        this.playerElements.clear();
        this.bombElements.clear();
        this.explosionElements.clear();
        this.powerupElements.clear();
        this.lastGameState = null;
    }
}

// Create singleton instance
export const gameRenderer = new GameRenderer();
