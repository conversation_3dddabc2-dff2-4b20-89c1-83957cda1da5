import { renderDOM } from '../framework/dom.js';
import { setState, getState } from '../framework/state.js';
import { onEvent } from '../framework/events.js';
import { navigate } from '../main.js';
import { wsManager } from '../utils/websocket.js';

export function NicknamePage() {
    const state = getState();
    
    const vdom = {
        tag: 'div',
        attrs: { class: 'nickname-container' },
        children: [
            {
                tag: 'h1',
                children: ['💣 BOMBERMAN DOM 💣']
            },
            {
                tag: 'p',
                attrs: { style: 'font-size: 1.2rem; margin-bottom: 30px; color: #ccc;' },
                children: ['Enter your nickname to join the battle!']
            },
            {
                tag: 'div',
                children: [
                    {
                        tag: 'input',
                        attrs: {
                            type: 'text',
                            class: 'nickname-input',
                            id: 'nickname-input',
                            placeholder: 'Enter your nickname...',
                            maxlength: '20',
                            value: state.nickname || ''
                        }
                    }
                ]
            },
            {
                tag: 'div',
                children: [
                    {
                        tag: 'button',
                        attrs: {
                            class: 'btn',
                            id: 'join-game-btn',
                            disabled: !state.nickname || state.nickname.trim().length < 2
                        },
                        children: ['Join Game']
                    }
                ]
            },
            {
                tag: 'div',
                attrs: { 
                    id: 'connection-status',
                    style: 'margin-top: 20px; font-size: 0.9rem;'
                },
                children: [
                    state.isConnected ? 
                        '🟢 Connected to server' : 
                        '🔴 Connecting to server...'
                ]
            }
        ]
    };

    const app = document.getElementById('app');
    renderDOM(vdom, app);

    // Set up event handlers
    setupNicknamePageEvents();
}

function setupNicknamePageEvents() {
    // Handle nickname input changes
    onEvent('input', '#nickname-input', (e) => {
        const nickname = e.target.value.trim();
        setState({ nickname });
        
        // Update button state
        const btn = document.getElementById('join-game-btn');
        if (btn) {
            btn.disabled = nickname.length < 2;
        }
    });

    // Handle Enter key in nickname input
    onEvent('keypress', '#nickname-input', (e) => {
        if (e.key === 'Enter') {
            const nickname = e.target.value.trim();
            if (nickname.length >= 2) {
                joinGame(nickname);
            }
        }
    });

    // Handle join game button click
    onEvent('click', '#join-game-btn', (e) => {
        const state = getState();
        if (state.nickname && state.nickname.trim().length >= 2) {
            joinGame(state.nickname.trim());
        }
    });
}

function joinGame(nickname) {
    console.log('Attempting to join game with nickname:', nickname);

    // Update state with nickname
    setState({ nickname });

    // Initialize WebSocket connection using the manager
    wsManager.connect(nickname);

    // Navigate to waiting room
    navigate('/waiting-room');
}
