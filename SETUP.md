# Bomberman DOM - Setup Instructions

## Overview
This is a multiplayer Bomberman game built using only DOM manipulation and a custom mini-framework. The game supports 2-4 players in real-time multiplayer battles.

## Features Implemented
- ✅ Nickname entry page
- ✅ Waiting room with player counter (2-4 players)
- ✅ Timer system (20s wait + 10s countdown)
- ✅ Real-time chat using WebSockets
- ✅ Game map with destructible blocks and indestructible walls
- ✅ Player movement with collision detection
- ✅ Bomb placement and explosion mechanics
- ✅ Power-ups (Bombs, Flames, Speed)
- ✅ Lives system (3 lives per player)
- ✅ Performance optimization with requestAnimationFrame
- ✅ 60fps game loop with minimal DOM manipulation
- ✅ Multiplayer synchronization

## Requirements
- Node.js (for the WebSocket server)
- Modern web browser with ES6 module support

## Installation & Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Start the Server
```bash
npm start
```

The server will start on `http://localhost:8080`

### 3. Open the Game
Open your web browser and navigate to `http://localhost:8080`

### 4. Testing Multiplayer
To test multiplayer functionality:
1. Open multiple browser tabs/windows
2. Or use different browsers
3. Or use incognito/private browsing mode
4. Each instance can join as a different player

## Game Controls
- **Movement**: Arrow keys or WASD
- **Place Bomb**: Spacebar or Enter
- **Chat**: Type in the chat box during waiting room

## Game Rules
1. **Players**: 2-4 players can join a game
2. **Lives**: Each player starts with 3 lives
3. **Objective**: Be the last player standing
4. **Map**: Fixed layout with destructible blocks and indestructible walls
5. **Power-ups**: Randomly appear when blocks are destroyed
   - 🧨 **Bombs**: Increase bomb capacity
   - 🔥 **Flames**: Increase explosion range
   - ⚡ **Speed**: Increase movement speed

## Game Flow
1. **Nickname Entry**: Enter your nickname to join
2. **Waiting Room**: Wait for other players (2-4 total)
   - 20 second waiting period for players to join
   - If 4 players join, countdown starts immediately
   - 10 second countdown before game starts
3. **Game**: Battle until one player remains
4. **Game Over**: Winner is announced, return to lobby

## Performance Features
- **60fps**: Consistent frame rate using requestAnimationFrame
- **Optimized Rendering**: Minimal DOM manipulation with efficient updates
- **Smooth Movement**: Sub-pixel positioning for fluid player movement
- **Efficient Collision Detection**: Optimized algorithms for real-time gameplay

## Technical Architecture
- **Frontend**: Custom mini-framework (DOM, State, Router, Events)
- **Backend**: Node.js WebSocket server
- **Communication**: Real-time WebSocket messages
- **Rendering**: Pure DOM manipulation (no Canvas/WebGL)
- **State Management**: Centralized state with reactive updates

## File Structure
```
├── index.html              # Main HTML file
├── styles.css              # Game styles
├── main.js                 # Application entry point
├── framework/              # Custom mini-framework
│   ├── dom.js             # DOM manipulation utilities
│   ├── state.js           # State management
│   ├── router.js          # Client-side routing
│   └── events.js          # Event handling
├── pages/                  # Page components
│   ├── nickname.js        # Nickname entry page
│   ├── waiting-room.js    # Waiting room with chat
│   └── game.js            # Main game page
├── utils/                  # Utility modules
│   ├── websocket.js       # WebSocket client manager
│   ├── game-logic.js      # Core game mechanics
│   ├── renderer.js        # Optimized game renderer
│   └── multiplayer.js     # Multiplayer synchronization
├── server.js              # WebSocket server
└── package.json           # Node.js dependencies
```

## Development Notes
- The game uses only the provided mini-framework
- No external libraries except for the WebSocket server
- Performance optimized for 60fps gameplay
- Real-time multiplayer with lag compensation
- Responsive design for different screen sizes

## Troubleshooting
1. **WebSocket Connection Issues**: Make sure the server is running on port 8080
2. **Performance Issues**: Check browser developer tools for frame rate
3. **Multiplayer Sync Issues**: Ensure all players are connected to the same server
4. **Game Not Starting**: Make sure at least 2 players are in the waiting room

## Browser Compatibility
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

The game requires ES6 module support and modern JavaScript features.
