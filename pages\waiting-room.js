import { renderDOM } from '../framework/dom.js';
import { setState, getState, subscribe } from '../framework/state.js';
import { onEvent } from '../framework/events.js';
import { navigate } from '../main.js';
import { wsManager } from '../utils/websocket.js';
import { gameLogic } from '../utils/game-logic.js';

let waitingTimer = null;
let countdownTimer = null;
let currentTimerValue = 0;
let timerType = 'waiting'; // 'waiting' or 'countdown'

export function WaitingRoomPage() {
    const state = getState();
    
    // Redirect if no nickname
    if (!state.nickname) {
        navigate('/');
        return;
    }

    // Initialize WebSocket if not connected
    if (!state.isConnected) {
        wsManager.connect(state.nickname);
    }

    renderWaitingRoom();
    setupWaitingRoomEvents();
    startWaitingTimer();
}

function renderWaitingRoom() {
    const state = getState();
    const playerCount = state.players ? state.players.length : 0;
    
    const vdom = {
        tag: 'div',
        attrs: { class: 'waiting-container' },
        children: [
            {
                tag: 'h1',
                children: ['🎮 Waiting Room 🎮']
            },
            {
                tag: 'div',
                attrs: { class: 'player-counter' },
                children: [`Players: ${playerCount}/4`]
            },
            {
                tag: 'div',
                attrs: { 
                    class: 'timer',
                    id: 'waiting-timer'
                },
                children: [getTimerText()]
            },
            {
                tag: 'div',
                attrs: { class: 'players-list' },
                children: [
                    {
                        tag: 'h3',
                        children: ['Players in lobby:']
                    },
                    ...renderPlayersList(state.players || [])
                ]
            },
            renderChatSection(state)
        ]
    };

    const app = document.getElementById('app');
    renderDOM(vdom, app);
}

function renderPlayersList(players) {
    return players.map(player => ({
        tag: 'div',
        attrs: { class: 'player-item' },
        children: [
            `🎯 ${player.nickname}`,
            player.id === getState().players?.[0]?.id ? ' (You)' : ''
        ]
    }));
}

function renderChatSection(state) {
    return {
        tag: 'div',
        attrs: { class: 'chat-container' },
        children: [
            {
                tag: 'h3',
                children: ['💬 Chat']
            },
            {
                tag: 'div',
                attrs: { 
                    class: 'chat-messages',
                    id: 'chat-messages'
                },
                children: renderChatMessages(state.chatMessages || [])
            },
            {
                tag: 'div',
                attrs: { class: 'chat-input-container' },
                children: [
                    {
                        tag: 'input',
                        attrs: {
                            type: 'text',
                            class: 'chat-input',
                            id: 'chat-input',
                            placeholder: 'Type a message...',
                            maxlength: '200'
                        }
                    },
                    {
                        tag: 'button',
                        attrs: {
                            class: 'btn',
                            id: 'send-chat-btn'
                        },
                        children: ['Send']
                    }
                ]
            }
        ]
    };
}

function renderChatMessages(messages) {
    return messages.map(msg => ({
        tag: 'div',
        attrs: { class: 'chat-message' },
        children: [
            {
                tag: 'span',
                attrs: { class: 'username' },
                children: [msg.username + ': ']
            },
            msg.message
        ]
    }));
}

function setupWaitingRoomEvents() {
    // Subscribe to state changes
    subscribe((state) => {
        updateWaitingRoom(state);
    });

    // Chat input events
    onEvent('keypress', '#chat-input', (e) => {
        if (e.key === 'Enter') {
            sendChatMessage();
        }
    });

    onEvent('click', '#send-chat-btn', () => {
        sendChatMessage();
    });

    // WebSocket message handlers
    wsManager.addMessageHandler('playerJoined', (message) => {
        checkGameStartConditions();
    });

    wsManager.addMessageHandler('playerLeft', (message) => {
        checkGameStartConditions();
    });

    wsManager.addMessageHandler('chatMessage', (message) => {
        scrollChatToBottom();
    });
}

function updateWaitingRoom(state) {
    // Update player counter
    const playerCount = state.players ? state.players.length : 0;
    const counterEl = document.querySelector('.player-counter');
    if (counterEl) {
        counterEl.textContent = `Players: ${playerCount}/4`;
    }

    // Update players list
    const playersListEl = document.querySelector('.players-list');
    if (playersListEl) {
        const playersListVdom = [
            {
                tag: 'h3',
                children: ['Players in lobby:']
            },
            ...renderPlayersList(state.players || [])
        ];
        
        // Clear and re-render players list
        playersListEl.innerHTML = '';
        playersListVdom.forEach(item => {
            const el = document.createElement(item.tag);
            if (item.attrs) {
                Object.keys(item.attrs).forEach(attr => {
                    el.setAttribute(attr, item.attrs[attr]);
                });
            }
            if (item.children) {
                item.children.forEach(child => {
                    if (typeof child === 'string') {
                        el.appendChild(document.createTextNode(child));
                    }
                });
            }
            playersListEl.appendChild(el);
        });
    }

    // Update chat messages
    const chatMessagesEl = document.getElementById('chat-messages');
    if (chatMessagesEl && state.chatMessages) {
        chatMessagesEl.innerHTML = '';
        state.chatMessages.forEach(msg => {
            const msgEl = document.createElement('div');
            msgEl.className = 'chat-message';
            
            const usernameEl = document.createElement('span');
            usernameEl.className = 'username';
            usernameEl.textContent = msg.username + ': ';
            
            msgEl.appendChild(usernameEl);
            msgEl.appendChild(document.createTextNode(msg.message));
            
            chatMessagesEl.appendChild(msgEl);
        });
        scrollChatToBottom();
    }

    checkGameStartConditions();
}

function sendChatMessage() {
    const input = document.getElementById('chat-input');
    if (input && input.value.trim()) {
        const message = input.value.trim();
        wsManager.sendChatMessage(message);
        input.value = '';
    }
}

function scrollChatToBottom() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

function startWaitingTimer() {
    if (waitingTimer) {
        clearInterval(waitingTimer);
    }
    
    currentTimerValue = 20;
    timerType = 'waiting';
    updateTimerDisplay();
    
    waitingTimer = setInterval(() => {
        currentTimerValue--;
        updateTimerDisplay();
        
        if (currentTimerValue <= 0) {
            clearInterval(waitingTimer);
            startCountdownTimer();
        }
    }, 1000);
}

function startCountdownTimer() {
    const state = getState();
    const playerCount = state.players ? state.players.length : 0;
    
    if (playerCount < 2) {
        // Not enough players, restart waiting timer
        startWaitingTimer();
        return;
    }
    
    if (countdownTimer) {
        clearInterval(countdownTimer);
    }
    
    currentTimerValue = 10;
    timerType = 'countdown';
    updateTimerDisplay();
    
    countdownTimer = setInterval(() => {
        currentTimerValue--;
        updateTimerDisplay();
        
        if (currentTimerValue <= 0) {
            clearInterval(countdownTimer);
            startGame();
        }
    }, 1000);
}

function checkGameStartConditions() {
    const state = getState();
    const playerCount = state.players ? state.players.length : 0;
    
    if (playerCount >= 4 && timerType === 'waiting') {
        // 4 players reached, start countdown immediately
        clearInterval(waitingTimer);
        startCountdownTimer();
    } else if (playerCount < 2 && timerType === 'countdown') {
        // Not enough players during countdown, restart waiting
        clearInterval(countdownTimer);
        startWaitingTimer();
    }
}

function updateTimerDisplay() {
    const timerEl = document.getElementById('waiting-timer');
    if (timerEl) {
        timerEl.textContent = getTimerText();
    }
}

function getTimerText() {
    if (timerType === 'waiting') {
        return `⏰ Waiting for players... ${currentTimerValue}s`;
    } else {
        return `🚀 Game starting in ${currentTimerValue}s!`;
    }
}

function startGame() {
    console.log('Starting game!');
    
    // Clear any existing timers
    if (waitingTimer) clearInterval(waitingTimer);
    if (countdownTimer) clearInterval(countdownTimer);
    
    // Initialize game state
    const state = getState();
    const gameState = initializeGameState(state.players);
    setState({ gameState });
    
    // Navigate to game
    navigate('/game');
}

function initializeGameState(players) {
    // Create initial game state
    const gameState = {
        players: players.map((player, index) => ({
            ...player,
            x: gameLogic.getStartingPosition(index).x,
            y: gameLogic.getStartingPosition(index).y,
            lives: 3,
            powerups: {
                bombs: 1,
                flames: 1,
                speed: 1
            },
            isAlive: true
        })),
        map: gameLogic.generateMap(),
        bombs: [],
        explosions: [],
        powerups: [],
        gameStarted: true,
        winner: null
    };

    return gameState;
}
