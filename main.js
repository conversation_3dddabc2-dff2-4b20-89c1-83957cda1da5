import { renderDOM } from './framework/dom.js';
import { defineRoute, setDefaultRoute, renderRoute, navigate } from './framework/router.js';
import { setState, getState, subscribe } from './framework/state.js';
import { onEvent } from './framework/events.js';

// Import page components
import { NicknamePage } from './pages/nickname.js';
import { WaitingRoomPage } from './pages/waiting-room.js';
import { GamePage } from './pages/game.js';

// Global state initialization
setState({
    currentPage: 'nickname',
    nickname: '',
    players: [],
    gameState: null,
    websocket: null,
    isConnected: false
});

// Define routes
defineRoute('/', NicknamePage);
defineRoute('/waiting-room', WaitingRoomPage);
defineRoute('/game', GamePage);

// Set default route
setDefaultRoute('/');

// Subscribe to state changes for re-rendering
subscribe((state) => {
    console.log('State updated:', state);
});

// Initialize the application
function init() {
    console.log('Bomberman DOM starting...');
    renderRoute();
}

// Start the application when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}

// Export navigate function for use in other modules
export { navigate };
