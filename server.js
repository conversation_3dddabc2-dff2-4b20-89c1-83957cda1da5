// Simple WebSocket server for development testing
// Run with: node server.js

const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const fs = require('fs');

// Create HTTP server for serving static files
const server = http.createServer((req, res) => {
    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }

    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html',
        '.js': 'text/javascript',
        '.css': 'text/css',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm'
    };

    const contentType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>', 'utf-8');
            } else {
                res.writeHead(500);
                res.end('Sorry, check with the site admin for error: ' + error.code + ' ..\n');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Game state
let gameRooms = new Map();
let players = new Map();

class GameRoom {
    constructor() {
        this.players = [];
        this.gameState = null;
        this.isGameStarted = false;
        this.waitingTimer = null;
        this.countdownTimer = null;
        this.host = null;
    }

    addPlayer(player) {
        this.players.push(player);
        player.room = this;
        
        // Assign host if first player
        if (this.players.length === 1) {
            this.host = player;
            player.send(JSON.stringify({
                type: 'hostAssignment',
                isHost: true
            }));
        }

        this.broadcastPlayerUpdate();
        this.checkGameStart();
    }

    removePlayer(player) {
        const index = this.players.indexOf(player);
        if (index > -1) {
            this.players.splice(index, 1);
        }

        // Reassign host if needed
        if (this.host === player && this.players.length > 0) {
            this.host = this.players[0];
            this.host.send(JSON.stringify({
                type: 'hostAssignment',
                isHost: true
            }));
        }

        this.broadcastPlayerUpdate();
        this.checkGameStart();
    }

    broadcastPlayerUpdate() {
        const playerList = this.players.map(p => ({
            id: p.id,
            nickname: p.nickname,
            lives: p.lives || 3,
            powerups: p.powerups || { bombs: 1, flames: 1, speed: 1 }
        }));

        this.broadcast({
            type: 'playersUpdate',
            players: playerList
        });
    }

    checkGameStart() {
        const playerCount = this.players.length;
        
        if (playerCount >= 2 && playerCount <= 4 && !this.isGameStarted) {
            if (playerCount === 4) {
                this.startCountdown();
            } else if (!this.waitingTimer) {
                this.startWaitingTimer();
            }
        } else if (playerCount < 2) {
            this.cancelTimers();
        }
    }

    startWaitingTimer() {
        this.cancelTimers();
        
        let timeLeft = 20;
        this.broadcast({
            type: 'timerUpdate',
            timer: timeLeft,
            timerType: 'waiting'
        });

        this.waitingTimer = setInterval(() => {
            timeLeft--;
            this.broadcast({
                type: 'timerUpdate',
                timer: timeLeft,
                timerType: 'waiting'
            });

            if (timeLeft <= 0) {
                this.startCountdown();
            }
        }, 1000);
    }

    startCountdown() {
        this.cancelTimers();
        
        let timeLeft = 10;
        this.broadcast({
            type: 'timerUpdate',
            timer: timeLeft,
            timerType: 'countdown'
        });

        this.countdownTimer = setInterval(() => {
            timeLeft--;
            this.broadcast({
                type: 'timerUpdate',
                timer: timeLeft,
                timerType: 'countdown'
            });

            if (timeLeft <= 0) {
                this.startGame();
            }
        }, 1000);
    }

    cancelTimers() {
        if (this.waitingTimer) {
            clearInterval(this.waitingTimer);
            this.waitingTimer = null;
        }
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    }

    startGame() {
        this.cancelTimers();
        this.isGameStarted = true;

        // Initialize game state
        this.gameState = {
            players: this.players.map((player, index) => ({
                id: player.id,
                nickname: player.nickname,
                x: this.getStartingPosition(index).x,
                y: this.getStartingPosition(index).y,
                lives: 3,
                powerups: { bombs: 1, flames: 1, speed: 1 },
                isAlive: true
            })),
            map: this.generateMap(),
            bombs: [],
            explosions: [],
            powerups: [],
            gameStarted: true,
            winner: null
        };

        this.broadcast({
            type: 'gameStart',
            gameState: this.gameState
        });
    }

    getStartingPosition(index) {
        const positions = [
            { x: 1, y: 1 },
            { x: 13, y: 1 },
            { x: 1, y: 11 },
            { x: 13, y: 11 }
        ];
        return positions[index] || positions[0];
    }

    generateMap() {
        // Simple map generation - same as client-side
        const width = 15;
        const height = 13;
        const map = [];
        
        for (let y = 0; y < height; y++) {
            map[y] = [];
            for (let x = 0; x < width; x++) {
                map[y][x] = 'empty';
            }
        }
        
        // Add walls
        for (let y = 1; y < height; y += 2) {
            for (let x = 1; x < width; x += 2) {
                map[y][x] = 'wall';
            }
        }
        
        // Add border walls
        for (let x = 0; x < width; x++) {
            map[0][x] = 'wall';
            map[height - 1][x] = 'wall';
        }
        for (let y = 0; y < height; y++) {
            map[y][0] = 'wall';
            map[y][width - 1] = 'wall';
        }
        
        // Add random blocks
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (map[y][x] === 'empty' && Math.random() < 0.6) {
                    const isNearStart = (
                        (x <= 2 && y <= 2) ||
                        (x >= width - 3 && y <= 2) ||
                        (x <= 2 && y >= height - 3) ||
                        (x >= width - 3 && y >= height - 3)
                    );
                    
                    if (!isNearStart) {
                        map[y][x] = 'block';
                    }
                }
            }
        }
        
        return map;
    }

    broadcast(message) {
        const messageStr = JSON.stringify(message);
        this.players.forEach(player => {
            if (player.ws.readyState === WebSocket.OPEN) {
                player.ws.send(messageStr);
            }
        });
    }
}

// Default room for all players
const defaultRoom = new GameRoom();
gameRooms.set('default', defaultRoom);

wss.on('connection', (ws) => {
    console.log('New WebSocket connection');

    let player = null;

    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);
            console.log('Received:', data);

            switch (data.type) {
                case 'join':
                    player = {
                        id: 'player_' + Date.now() + '_' + Math.random(),
                        nickname: data.nickname,
                        ws: ws,
                        room: null
                    };
                    players.set(player.id, player);
                    defaultRoom.addPlayer(player);
                    break;

                case 'chatMessage':
                    if (player && player.room) {
                        player.room.broadcast({
                            type: 'chatMessage',
                            username: player.nickname,
                            message: data.message
                        });
                    }
                    break;

                case 'playerInput':
                case 'playerAction':
                case 'gameStateUpdate':
                    // Forward to all players in room
                    if (player && player.room) {
                        player.room.broadcast(data);
                    }
                    break;
            }
        } catch (error) {
            console.error('Error parsing message:', error);
        }
    });

    ws.on('close', () => {
        console.log('WebSocket connection closed');
        if (player) {
            if (player.room) {
                player.room.removePlayer(player);
            }
            players.delete(player.id);
        }
    });

    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
    });
});

const PORT = process.env.PORT || 8080;
server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log(`WebSocket server running on ws://localhost:${PORT}`);
});
