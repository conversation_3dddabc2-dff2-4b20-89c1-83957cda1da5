import { setState, getState } from '../framework/state.js';
import { multiplayerManager } from './multiplayer.js';

export class GameLogic {
    constructor() {
        this.keyState = {};
        this.setupKeyboardListeners();
    }

    setupKeyboardListeners() {
        document.addEventListener('keydown', (e) => {
            this.keyState[e.key.toLowerCase()] = true;
            this.handleKeyPress(e.key.toLowerCase(), e);
        });

        document.addEventListener('keyup', (e) => {
            this.keyState[e.key.toLowerCase()] = false;
        });

        // Prevent default behavior for game keys
        document.addEventListener('keydown', (e) => {
            const gameKeys = ['arrowup', 'arrowdown', 'arrowleft', 'arrowright', ' ', 'enter', 'w', 'a', 's', 'd'];
            if (gameKeys.includes(e.key.toLowerCase())) {
                e.preventDefault();
            }
        });
    }

    handleKeyPress(key, event) {
        const state = getState();
        const gameState = state.gameState;

        if (!gameState || gameState.winner) return;

        const currentPlayer = this.getCurrentPlayer();
        if (!currentPlayer || !currentPlayer.isAlive) return;

        switch (key) {
            case ' ':
            case 'enter':
                multiplayerManager.handleLocalBombPlacement();
                break;
        }
    }

    getCurrentPlayer() {
        const state = getState();
        if (!state.gameState || !state.nickname) return null;
        
        return state.gameState.players.find(p => p.nickname === state.nickname);
    }

    updatePlayerMovement(gameState, deltaTime) {
        // Send input to multiplayer manager
        multiplayerManager.handleLocalInput(this.keyState);

        // Apply interpolation for smooth movement
        multiplayerManager.interpolatePlayerPositions(gameState, deltaTime);

        // If we're the host, also broadcast game state periodically
        if (multiplayerManager.isHost) {
            multiplayerManager.broadcastGameState();
        }
    }

    canMoveTo(x, y, gameState) {
        const cellX = Math.floor(x);
        const cellY = Math.floor(y);
        
        // Check bounds
        if (cellY < 0 || cellY >= gameState.map.length || 
            cellX < 0 || cellX >= gameState.map[0].length) {
            return false;
        }
        
        // Check cell type
        const cellType = gameState.map[cellY][cellX];
        if (cellType === 'wall' || cellType === 'block') {
            return false;
        }
        
        // Check for bombs (players can walk over their own bombs)
        const currentPlayer = this.getCurrentPlayer();
        const bombAtPosition = gameState.bombs.find(b => b.x === cellX && b.y === cellY);
        if (bombAtPosition && bombAtPosition.playerId !== currentPlayer.id) {
            return false;
        }
        
        return true;
    }

    placeBomb(player, gameState) {
        const bombX = Math.floor(player.x);
        const bombY = Math.floor(player.y);
        
        // Check if there's already a bomb at this position
        const existingBomb = gameState.bombs.find(b => b.x === bombX && b.y === bombY);
        if (existingBomb) return false;
        
        // Check if player has bombs available
        const playerBombs = gameState.bombs.filter(b => b.playerId === player.id);
        if (playerBombs.length >= player.powerups.bombs) return false;
        
        const bomb = {
            id: Date.now() + Math.random(),
            x: bombX,
            y: bombY,
            playerId: player.id,
            timer: 3000, // 3 seconds
            range: player.powerups.flames
        };
        
        gameState.bombs.push(bomb);
        return true;
    }

    updateBombs(gameState, deltaTime) {
        gameState.bombs = gameState.bombs.filter(bomb => {
            bomb.timer -= deltaTime;
            
            if (bomb.timer <= 0) {
                this.explodeBomb(bomb, gameState);
                return false;
            }
            
            return true;
        });
    }

    explodeBomb(bomb, gameState) {
        const explosions = [];
        const directions = [
            { dx: 0, dy: 0 },   // Center
            { dx: 1, dy: 0 },   // Right
            { dx: -1, dy: 0 },  // Left
            { dx: 0, dy: 1 },   // Down
            { dx: 0, dy: -1 }   // Up
        ];
        
        directions.forEach(dir => {
            for (let i = 0; i < (dir.dx === 0 && dir.dy === 0 ? 1 : bomb.range); i++) {
                const x = bomb.x + dir.dx * i;
                const y = bomb.y + dir.dy * i;
                
                if (y < 0 || y >= gameState.map.length || 
                    x < 0 || x >= gameState.map[0].length) {
                    break;
                }
                
                const cellType = gameState.map[y][x];
                
                if (cellType === 'wall') {
                    break;
                }
                
                explosions.push({
                    id: Date.now() + Math.random() + i,
                    x,
                    y,
                    timer: 500 // 0.5 seconds
                });
                
                if (cellType === 'block') {
                    gameState.map[y][x] = 'empty';
                    
                    // Chance to spawn power-up
                    if (Math.random() < 0.3) {
                        const powerupTypes = ['bombs', 'flames', 'speed'];
                        const randomType = powerupTypes[Math.floor(Math.random() * powerupTypes.length)];
                        
                        gameState.powerups.push({
                            id: Date.now() + Math.random(),
                            x,
                            y,
                            type: randomType
                        });
                    }
                    
                    break;
                }
            }
        });
        
        gameState.explosions.push(...explosions);
    }

    updateExplosions(gameState, deltaTime) {
        gameState.explosions = gameState.explosions.filter(explosion => {
            explosion.timer -= deltaTime;
            return explosion.timer > 0;
        });
    }

    checkCollisions(gameState) {
        gameState.players.forEach(player => {
            if (!player.isAlive) return;
            
            const playerX = Math.floor(player.x);
            const playerY = Math.floor(player.y);
            
            // Check explosion collision
            const hitByExplosion = gameState.explosions.some(e => 
                e.x === playerX && e.y === playerY
            );
            
            if (hitByExplosion) {
                player.lives--;
                if (player.lives <= 0) {
                    player.isAlive = false;
                }
                
                // Add invincibility frames to prevent multiple hits
                player.invincible = true;
                setTimeout(() => {
                    if (player.isAlive) {
                        player.invincible = false;
                    }
                }, 1000);
            }
            
            // Check power-up collision
            const powerupIndex = gameState.powerups.findIndex(p => 
                p.x === playerX && p.y === playerY
            );
            
            if (powerupIndex !== -1) {
                const powerup = gameState.powerups[powerupIndex];
                player.powerups[powerup.type]++;
                gameState.powerups.splice(powerupIndex, 1);
            }
        });
    }

    checkWinCondition(gameState) {
        const alivePlayers = gameState.players.filter(p => p.isAlive);
        
        if (alivePlayers.length <= 1) {
            gameState.winner = alivePlayers[0] || null;
            return true;
        }
        
        return false;
    }

    generateMap() {
        const width = 15;
        const height = 13;
        const map = [];
        
        // Initialize empty map
        for (let y = 0; y < height; y++) {
            map[y] = [];
            for (let x = 0; x < width; x++) {
                map[y][x] = 'empty';
            }
        }
        
        // Add walls (every other cell starting from 1,1)
        for (let y = 1; y < height; y += 2) {
            for (let x = 1; x < width; x += 2) {
                map[y][x] = 'wall';
            }
        }
        
        // Add border walls
        for (let x = 0; x < width; x++) {
            map[0][x] = 'wall';
            map[height - 1][x] = 'wall';
        }
        for (let y = 0; y < height; y++) {
            map[y][0] = 'wall';
            map[y][width - 1] = 'wall';
        }
        
        // Add random destructible blocks
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                if (map[y][x] === 'empty' && Math.random() < 0.6) {
                    // Don't place blocks near starting positions
                    const isNearStart = (
                        (x <= 2 && y <= 2) ||
                        (x >= width - 3 && y <= 2) ||
                        (x <= 2 && y >= height - 3) ||
                        (x >= width - 3 && y >= height - 3)
                    );
                    
                    if (!isNearStart) {
                        map[y][x] = 'block';
                    }
                }
            }
        }
        
        return map;
    }

    getStartingPosition(playerIndex) {
        const positions = [
            { x: 1, y: 1 },     // Top-left
            { x: 13, y: 1 },    // Top-right
            { x: 1, y: 11 },    // Bottom-left
            { x: 13, y: 11 }    // Bottom-right
        ];
        return positions[playerIndex] || positions[0];
    }
}

// Create singleton instance
export const gameLogic = new GameLogic();
