import { setState, getState } from '../framework/state.js';
import { wsManager } from './websocket.js';

export class MultiplayerManager {
    constructor() {
        this.isHost = false;
        this.lastSyncTime = 0;
        this.syncInterval = 50; // Sync every 50ms (20 times per second)
        this.inputBuffer = [];
        this.setupMessageHandlers();
    }

    setupMessageHandlers() {
        // Handle player input from other players
        wsManager.addMessageHandler('playerInput', (message) => {
            this.handlePlayerInput(message);
        });

        // Handle game state updates from host
        wsManager.addMessageHandler('gameStateUpdate', (message) => {
            this.handleGameStateUpdate(message);
        });

        // Handle player actions (bomb placement, etc.)
        wsManager.addMessageHandler('playerAction', (message) => {
            this.handlePlayerAction(message);
        });

        // Handle host assignment
        wsManager.addMessageHandler('hostAssignment', (message) => {
            this.isHost = message.isHost;
            console.log('Host status:', this.isHost);
        });
    }

    sendPlayerInput(input) {
        const state = getState();
        if (!state.isConnected || !state.nickname) return;

        const inputData = {
            type: 'playerInput',
            playerId: this.getCurrentPlayerId(),
            input: input,
            timestamp: Date.now()
        };

        wsManager.send(inputData);
    }

    sendPlayerAction(action) {
        const state = getState();
        if (!state.isConnected || !state.nickname) return;

        const actionData = {
            type: 'playerAction',
            playerId: this.getCurrentPlayerId(),
            action: action,
            timestamp: Date.now()
        };

        wsManager.send(actionData);
    }

    handlePlayerInput(message) {
        const state = getState();
        const gameState = state.gameState;
        
        if (!gameState || !this.isHost) return;

        // Find the player and apply input
        const player = gameState.players.find(p => p.id === message.playerId);
        if (player && player.isAlive) {
            this.applyPlayerInput(player, message.input);
        }
    }

    handlePlayerAction(message) {
        const state = getState();
        const gameState = state.gameState;
        
        if (!gameState) return;

        const player = gameState.players.find(p => p.id === message.playerId);
        if (!player || !player.isAlive) return;

        switch (message.action.type) {
            case 'placeBomb':
                this.handleBombPlacement(player, gameState);
                break;
        }

        // If we're the host, broadcast the updated game state
        if (this.isHost) {
            this.broadcastGameState();
        }
    }

    handleGameStateUpdate(message) {
        if (this.isHost) return; // Host doesn't receive state updates

        const state = getState();
        setState({
            gameState: message.gameState
        });
    }

    applyPlayerInput(player, input) {
        // Apply movement input to player
        const speed = 0.1; // Adjust based on your game's speed
        
        if (input.up) player.y = Math.max(0, player.y - speed);
        if (input.down) player.y = Math.min(12, player.y + speed);
        if (input.left) player.x = Math.max(0, player.x - speed);
        if (input.right) player.x = Math.min(14, player.x + speed);
    }

    handleBombPlacement(player, gameState) {
        const bombX = Math.floor(player.x);
        const bombY = Math.floor(player.y);
        
        // Check if there's already a bomb at this position
        const existingBomb = gameState.bombs.find(b => b.x === bombX && b.y === bombY);
        if (existingBomb) return;
        
        // Check if player has bombs available
        const playerBombs = gameState.bombs.filter(b => b.playerId === player.id);
        if (playerBombs.length >= player.powerups.bombs) return;
        
        const bomb = {
            id: Date.now() + Math.random(),
            x: bombX,
            y: bombY,
            playerId: player.id,
            timer: 3000,
            range: player.powerups.flames
        };
        
        gameState.bombs.push(bomb);
    }

    broadcastGameState() {
        if (!this.isHost) return;

        const state = getState();
        const gameState = state.gameState;
        
        if (!gameState) return;

        const now = Date.now();
        if (now - this.lastSyncTime < this.syncInterval) return;

        wsManager.send({
            type: 'gameStateUpdate',
            gameState: gameState,
            timestamp: now
        });

        this.lastSyncTime = now;
    }

    getCurrentPlayerId() {
        const state = getState();
        if (!state.gameState || !state.nickname) return null;
        
        const player = state.gameState.players.find(p => p.nickname === state.nickname);
        return player ? player.id : null;
    }

    // Method to handle local player input and send to other players
    handleLocalInput(keyState) {
        const input = {
            up: keyState['arrowup'] || keyState['w'],
            down: keyState['arrowdown'] || keyState['s'],
            left: keyState['arrowleft'] || keyState['a'],
            right: keyState['arrowright'] || keyState['d']
        };

        // Send input to other players
        this.sendPlayerInput(input);

        // If we're the host, also apply locally
        if (this.isHost) {
            const state = getState();
            const gameState = state.gameState;
            const currentPlayer = gameState.players.find(p => p.nickname === state.nickname);
            
            if (currentPlayer && currentPlayer.isAlive) {
                this.applyPlayerInput(currentPlayer, input);
            }
        }
    }

    // Method to handle local bomb placement
    handleLocalBombPlacement() {
        const action = {
            type: 'placeBomb'
        };

        this.sendPlayerAction(action);

        // If we're the host, also apply locally
        if (this.isHost) {
            const state = getState();
            const gameState = state.gameState;
            const currentPlayer = gameState.players.find(p => p.nickname === state.nickname);
            
            if (currentPlayer && currentPlayer.isAlive) {
                this.handleBombPlacement(currentPlayer, gameState);
            }
        }
    }

    // Interpolation for smooth movement
    interpolatePlayerPositions(gameState, deltaTime) {
        if (this.isHost) return; // Host doesn't need interpolation

        gameState.players.forEach(player => {
            if (!player.isAlive) return;

            // Simple linear interpolation
            // In a real implementation, you'd want more sophisticated prediction
            const speed = player.powerups.speed * 0.003;
            const movement = speed * deltaTime;

            // This is a simplified version - you'd want to store target positions
            // and interpolate towards them based on received updates
        });
    }

    // Lag compensation for actions
    compensateForLag(action, timestamp) {
        const now = Date.now();
        const lag = now - timestamp;
        
        // Compensate for network lag by rewinding game state
        // This is a complex topic and would require storing game state history
        // For now, we'll just apply actions immediately
        
        return action;
    }

    // Conflict resolution for simultaneous actions
    resolveConflicts(actions) {
        // Sort actions by timestamp to ensure deterministic order
        return actions.sort((a, b) => a.timestamp - b.timestamp);
    }

    cleanup() {
        this.isHost = false;
        this.inputBuffer = [];
        this.lastSyncTime = 0;
    }
}

// Create singleton instance
export const multiplayerManager = new MultiplayerManager();
