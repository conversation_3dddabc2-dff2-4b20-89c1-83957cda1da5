import { setState, getState } from '../framework/state.js';
import { navigate } from '../main.js';

class WebSocketManager {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.messageHandlers = new Map();
    }

    connect(nickname) {
        try {
            // Try to connect to WebSocket server
            this.ws = new WebSocket('ws://localhost:8080');
            
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.reconnectAttempts = 0;
                setState({ 
                    websocket: this.ws, 
                    isConnected: true 
                });
                
                // Send join message
                this.send({
                    type: 'join',
                    nickname: nickname
                });
            };
            
            this.ws.onmessage = (event) => {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                setState({ 
                    websocket: null, 
                    isConnected: false 
                });
                this.attemptReconnect(nickname);
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                // For development, fall back to simulation
                this.simulateConnection(nickname);
            };
            
        } catch (error) {
            console.error('Failed to create WebSocket:', error);
            this.simulateConnection(nickname);
        }
    }

    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket not connected, message not sent:', message);
        }
    }

    addMessageHandler(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }

    removeMessageHandler(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    handleMessage(message) {
        console.log('Received WebSocket message:', message);
        
        // Call registered handlers
        if (this.messageHandlers.has(message.type)) {
            this.messageHandlers.get(message.type).forEach(handler => {
                handler(message);
            });
        }
        
        // Default message handling
        switch (message.type) {
            case 'playerJoined':
                this.handlePlayerJoined(message);
                break;
            case 'playerLeft':
                this.handlePlayerLeft(message);
                break;
            case 'playersUpdate':
                this.handlePlayersUpdate(message);
                break;
            case 'gameStart':
                this.handleGameStart(message);
                break;
            case 'chatMessage':
                this.handleChatMessage(message);
                break;
            case 'gameState':
                this.handleGameState(message);
                break;
            case 'timerUpdate':
                this.handleTimerUpdate(message);
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }

    handlePlayerJoined(message) {
        const state = getState();
        const existingPlayer = state.players.find(p => p.id === message.player.id);
        if (!existingPlayer) {
            setState({
                players: [...state.players, message.player]
            });
        }
    }

    handlePlayerLeft(message) {
        const state = getState();
        setState({
            players: state.players.filter(p => p.id !== message.playerId)
        });
    }

    handlePlayersUpdate(message) {
        setState({
            players: message.players
        });
    }

    handleGameStart(message) {
        setState({
            gameState: message.gameState
        });
        navigate('/game');
    }

    handleChatMessage(message) {
        const state = getState();
        const chatMessages = state.chatMessages || [];
        setState({
            chatMessages: [...chatMessages, {
                id: Date.now(),
                username: message.username,
                message: message.message,
                timestamp: new Date()
            }]
        });
    }

    handleGameState(message) {
        setState({
            gameState: message.gameState
        });
    }

    handleTimerUpdate(message) {
        setState({
            timer: message.timer,
            timerType: message.timerType
        });
    }

    attemptReconnect(nickname) {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.connect(nickname);
            }, this.reconnectDelay * this.reconnectAttempts);
        } else {
            console.error('Max reconnection attempts reached');
            setState({ 
                connectionError: 'Failed to connect to server. Please refresh the page.' 
            });
        }
    }

    simulateConnection(nickname) {
        console.log('Simulating WebSocket connection for development');
        
        // Create a mock player
        const mockPlayer = {
            id: 'player_' + Date.now(),
            nickname: nickname,
            lives: 3,
            powerups: {
                bombs: 1,
                flames: 1,
                speed: 1
            }
        };
        
        setState({ 
            isConnected: true,
            players: [mockPlayer],
            chatMessages: [
                {
                    id: 1,
                    username: 'System',
                    message: 'Welcome to Bomberman DOM! (Development Mode)',
                    timestamp: new Date()
                }
            ]
        });
        
        // Simulate other players joining after a delay
        setTimeout(() => {
            const state = getState();
            if (state.players.length < 4) {
                const botPlayer = {
                    id: 'bot_' + Date.now(),
                    nickname: 'Bot Player',
                    lives: 3,
                    powerups: {
                        bombs: 1,
                        flames: 1,
                        speed: 1
                    }
                };
                setState({
                    players: [...state.players, botPlayer]
                });
            }
        }, 3000);
    }

    sendChatMessage(message) {
        const state = getState();
        this.send({
            type: 'chatMessage',
            username: state.nickname,
            message: message
        });
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        setState({ 
            websocket: null, 
            isConnected: false 
        });
    }
}

// Create singleton instance
export const wsManager = new WebSocketManager();
