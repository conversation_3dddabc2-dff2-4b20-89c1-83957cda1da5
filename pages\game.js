import { renderDOM } from '../framework/dom.js';
import { setState, getState, subscribe } from '../framework/state.js';
import { onEvent } from '../framework/events.js';
import { navigate } from '../main.js';
import { wsManager } from '../utils/websocket.js';
import { gameLogic } from '../utils/game-logic.js';
import { gameRenderer } from '../utils/renderer.js';

let gameLoop = null;
let lastFrameTime = 0;
const targetFPS = 60;
const frameTime = 1000 / targetFPS;

export function GamePage() {
    const state = getState();
    
    // Redirect if no game state
    if (!state.gameState) {
        navigate('/');
        return;
    }

    renderGame();
    setupGameEvents();
    initializeRenderer();
    startGameLoop();
}

function renderGame() {
    const state = getState();
    const gameState = state.gameState;
    
    if (!gameState) return;

    const vdom = {
        tag: 'div',
        attrs: { class: 'game-container' },
        children: [
            renderGameUI(gameState),
            renderGameBoard(gameState),
            gameState.winner ? renderGameOver(gameState) : null
        ].filter(Boolean)
    };

    const app = document.getElementById('app');
    renderDOM(vdom, app);
}

function renderGameUI(gameState) {
    const currentPlayer = getCurrentPlayer(gameState);
    
    return {
        tag: 'div',
        attrs: { class: 'game-ui' },
        children: [
            {
                tag: 'div',
                attrs: { class: 'player-stats' },
                children: [
                    `Player: ${currentPlayer ? currentPlayer.nickname : 'Unknown'}`
                ]
            },
            {
                tag: 'div',
                attrs: { class: 'lives' },
                children: [
                    `Lives: ${'❤️'.repeat(currentPlayer ? currentPlayer.lives : 0)}`
                ]
            },
            {
                tag: 'div',
                children: [
                    `Bombs: ${currentPlayer ? currentPlayer.powerups.bombs : 0}`
                ]
            },
            {
                tag: 'div',
                children: [
                    `Flame Range: ${currentPlayer ? currentPlayer.powerups.flames : 0}`
                ]
            },
            {
                tag: 'div',
                children: [
                    `Speed: ${currentPlayer ? currentPlayer.powerups.speed : 0}`
                ]
            }
        ]
    };
}

function renderGameBoard(gameState) {
    return {
        tag: 'div',
        attrs: {
            class: 'game-board',
            id: 'game-board'
        },
        children: []
    };
}

function renderGameOver(gameState) {
    return {
        tag: 'div',
        attrs: { class: 'game-over' },
        children: [
            {
                tag: 'h2',
                children: [gameState.winner ? `🎉 ${gameState.winner.nickname} Wins! 🎉` : 'Game Over']
            },
            {
                tag: 'button',
                attrs: {
                    class: 'btn',
                    id: 'back-to-lobby-btn'
                },
                children: ['Back to Lobby']
            }
        ]
    };
}

function setupGameEvents() {
    // Game over button
    onEvent('click', '#back-to-lobby-btn', () => {
        stopGameLoop();
        gameRenderer.cleanup();
        navigate('/');
    });

    // Subscribe to state changes
    subscribe((state) => {
        if (state.gameState) {
            gameRenderer.render(state.gameState);
        }
    });
}

function initializeRenderer() {
    const gameBoard = document.getElementById('game-board');
    if (gameBoard) {
        gameRenderer.initialize(gameBoard);
    }
}

function startGameLoop() {
    if (gameLoop) {
        cancelAnimationFrame(gameLoop);
    }
    
    lastFrameTime = performance.now();
    gameLoop = requestAnimationFrame(updateGame);
}

function updateGame(currentTime) {
    const deltaTime = currentTime - lastFrameTime;

    if (deltaTime >= frameTime) {
        const state = getState();
        const gameState = state.gameState;

        if (gameState && !gameState.winner) {
            gameLogic.updatePlayerMovement(gameState, deltaTime);
            gameLogic.updateBombs(gameState, deltaTime);
            gameLogic.updateExplosions(gameState, deltaTime);
            gameLogic.checkCollisions(gameState);

            if (gameLogic.checkWinCondition(gameState)) {
                stopGameLoop();
            }

            setState({ gameState });
        }

        lastFrameTime = currentTime;
    }

    const currentState = getState();
    if (!currentState.gameState?.winner) {
        gameLoop = requestAnimationFrame(updateGame);
    }
}



function stopGameLoop() {
    if (gameLoop) {
        cancelAnimationFrame(gameLoop);
        gameLoop = null;
    }
}
